<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="1000px"
    :before-close="handleClose"
    :show-close="false"
    append-to-body>

    <div v-loading="loading" class="detail-content">
      <!-- 项目/任务名称标题 -->
      <div class="project-title">
        项目/任务名称：{{ detailData.projectTaskName || '-' }}
      </div>

      <!-- 表单内容区域 -->
      <div class="form-content">
        <!-- 第一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">业务类型</div>
            <div class="form-value">{{ detailData.businessTypeName || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">状态</div>
            <div class="form-value">{{ getStatusLabel(detailData.status) }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">优先级</div>
            <div class="form-value">{{ detailData.priorityLevel || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">完成时间</div>
            <div class="form-value">{{ formatTime(detailData.completionTime) }}</div>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="form-row">
          <div class="form-item large">
            <div class="form-label">项目里程碑</div>
            <div class="form-value" v-html="formatMilestone(detailData)"></div>
          </div>
          <div class="form-item">
            <div class="form-label">任务说明/进度</div>
            <div class="form-value" v-html="formatProgress(detailData)"></div>
          </div>
          <div class="form-item">
            <div class="form-label">干系人</div>
            <div class="form-value" v-html="formatStakeholders(detailData)"></div>
          </div>
          <div class="form-item">
            <div class="form-label">投入人力</div>
            <div class="form-value" v-html="formatManpower(detailData)"></div>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">工作量（人日）</div>
            <div class="form-value" v-html="formatWorkload(detailData)"></div>
          </div>
          <div class="form-item">
            <div class="form-label">负责项目经理</div>
            <div class="form-value">{{ detailData.projectManagers || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">所属业务大类</div>
            <div class="form-value">{{ getDictLabel(detailData.businessCategoryMajor, dict.type.project_outcome_business_category_major) || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">所属业务小类</div>
            <div class="form-value">{{ getDictLabel(detailData.businessCategoryMinor, dict.type.project_outcome_business_category_minor) || '-' }}</div>
          </div>
        </div>

        <!-- 第四行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">成果类型</div>
            <div class="form-value">{{ getDictLabel(detailData.resultType, dict.type.project_outcome_types) || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">创建人</div>
            <div class="form-value">{{ detailData.createdBy || '-' }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">创建时间</div>
            <div class="form-value">{{ formatTime(detailData.createdTime) }}</div>
          </div>
          <div class="form-item">
            <!-- 空白占位 -->
          </div>
        </div>
      </div>


      <!-- 需求背景展示框 -->
      <div class="requirement-background">
        <div class="background-label">需求背景</div>
        <div class="background-content">
          {{ detailData.requirementBackground || 'xxxxxxxxx...............' }}
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { projectResultDetail } from "@/api/project/projectResult"
import { parseTime } from "@/utils/ruoyi"

export default {
  name: "DetailDialog",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '项目成果详情'
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      detailData: {}
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.recordId) {
        this.getDetailData()
      }
    },
    recordId(val) {
      if (val && this.dialogVisible) {
        this.getDetailData()
      }
    }
  },
  methods: {
    /** 获取详情数据 */
    getDetailData() {
      if (!this.recordId) return
      
      this.loading = true
      projectResultDetail(this.recordId).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.detailData = res.data || {}
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.$emit('update:dialogVisible', false)
      this.detailData = {}
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning', 
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },
    
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      if (!value || !dictData) return value
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },

    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return '-'
      return parseTime(date, '{y}-{m}-{d}')
    },

    /** 获取状态标签 */
    getStatusLabel(status) {
      const statusMap = {
        '1': '未开始',
        '2': '进行中',
        '3': '已完成',
        '4': '已取消'
      }
      return statusMap[status] || status
    },

    /** 格式化项目里程碑 */
    formatMilestone(row) {
      const milestones = []
      if (row.milestoneRequirements) {
        milestones.push(`完成评审：${this.formatDate(row.milestoneRequirements)}`)
      }
      if (row.milestoneDevelopment) {
        milestones.push(`完成开发：${this.formatDate(row.milestoneDevelopment)}`)
      }
      if (row.milestoneTest) {
        milestones.push(`完成测试验收：${this.formatDate(row.milestoneTest)}`)
      }
      if (row.milestoneOnline) {
        milestones.push(`完成上线：${this.formatDate(row.milestoneOnline)}`)
      }
      return milestones.join('<br/>')
    },

    /** 格式化任务说明/进度 */
    formatProgress(row) {
      const progress = []
      if (row.requirementsProgress !== null && row.requirementsProgress !== undefined) {
        progress.push(`需求评审：${row.requirementsProgress}%`)
      }
      if (row.developmentProgress !== null && row.developmentProgress !== undefined) {
        progress.push(`开发进度：${row.developmentProgress}%`)
      }
      if (row.testProgress !== null && row.testProgress !== undefined) {
        progress.push(`测试验收进度：${row.testProgress}%`)
      }
      return progress.join('<br/>')
    },

    /** 格式化干系人 */
    formatStakeholders(row) {
      const stakeholders = []
      if (row.productManagers) {
        stakeholders.push(`产品：${row.productManagers}`)
      }
      if (row.devTeams) {
        const devTeamNames = this.convertDeptIdsToNames(row.devTeams, 'dev')
        stakeholders.push(`开发：${devTeamNames}`)
      }
      if (row.testTeams) {
        const testTeamNames = this.convertDeptIdsToNames(row.testTeams, 'test')
        stakeholders.push(`测试：${testTeamNames}`)
      }
      return stakeholders.join('<br/>')
    },

    /** 格式化投入人力 */
    formatManpower(row) {
      const manpower = []
      if (row.devManpower) {
        manpower.push(`开发：${row.devManpower}人`)
      }
      if (row.testManpower) {
        manpower.push(`测试：${row.testManpower}人`)
      }
      return manpower.join('<br/>')
    },

    /** 格式化工作量（人日） */
    formatWorkload(row) {
      const workload = []
      if (row.devWorkload) {
        workload.push(`开发：${row.devWorkload}人日`)
      }
      if (row.testWorkload) {
        workload.push(`测试：${row.testWorkload}人日`)
      }
      return workload.join('<br/>')
    },

    /** 将部门ID转换为部门名称 */
    convertDeptIdsToNames(deptIds, type) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const deptDict = type === 'dev' ? this.dict.type.project_outcome_dev_dept : this.dict.type.project_outcome_test_dept
      if (!deptDict) return deptIds

      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    }
  }
}
</script>

<style scoped>
.detail-content {
  padding: 20px;
  background: #f8f9fa;
}

/* 项目标题样式 */
.project-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30px;
  padding: 15px;
  border: 2px solid #e74c3c;
  border-radius: 4px;
  background: white;
}

/* 表单内容区域 */
.form-content {
  margin-bottom: 20px;
}

/* 表单行样式 */
.form-row {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
}

/* 表单项样式 */
.form-item {
  flex: 1;
  border: 2px solid #e74c3c;
  border-radius: 4px;
  padding: 15px;
  background: white;
  min-height: 80px;
  display: flex;
  flex-direction: column;
}

.form-item.large {
  flex: 2;
}

/* 表单标签样式 */
.form-label {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
  text-align: center;
}

/* 表单值样式 */
.form-value {
  color: #606266;
  line-height: 1.6;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-all;
}

/* 需求背景展示框 */
.requirement-background {
  border: 2px solid #e74c3c;
  border-radius: 4px;
  background: white;
  padding: 20px;
  margin-top: 20px;
}

.background-label {
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  text-align: center;
  font-size: 16px;
}

.background-content {
  color: #606266;
  line-height: 1.6;
  min-height: 100px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  word-break: break-all;
}

/* 对话框底部 */
.dialog-footer {
  text-align: center;
  padding: 20px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .form-row {
    flex-wrap: wrap;
  }

  .form-item {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .form-item {
    min-width: 100%;
  }
}
</style>
