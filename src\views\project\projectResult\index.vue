<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="query-form">
      <el-form-item label="业务类型" prop="businessTypeId">
        <el-select v-model="queryParams.businessTypeId" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in businessTypeOptions" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in dict.type.project_outcome_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="负责项目经理" prop="projectManagers">
        <el-select v-model="queryParams.projectManagers" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="项目/任务" prop="projectTaskName">
        <el-input v-model="queryParams.projectTaskName" placeholder="请输入项目/任务名称" clearable style="width: 200px" />
      </el-form-item>

      <el-form-item label="成果类型" prop="resultType">
        <el-select v-model="queryParams.resultType" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="优先级" prop="priorityLevel">
        <el-select v-model="queryParams.priorityLevel" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in dict.type.project_outcome_priority_level" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="请选择时间"
          end-placeholder="请选择时间"
          style="width: 350px">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini">归档</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini">发送邮件</el-button>
      </el-col>
      <el-col :span="20" style="text-align: right;">
        <el-button type="primary" size="mini" @click="handleBusinessTypeManage">业务类型管理</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="tableData" 
      @sort-change="handleSortChange" 
      height="calc(100vh - 400px)" 
      stripe 
      border>
      
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      
      <el-table-column label="业务类型" align="center" prop="businessTypeName" width="100" show-overflow-tooltip />

      <el-table-column label="成果类型" align="center" prop="resultType" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.resultType, dict.type.project_outcome_types) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="mini">
            {{ getDictLabel(scope.row.status, dict.type.project_outcome_status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="项目/任务名称" align="center" prop="projectTaskName" min-width="150" show-overflow-tooltip />
      
      <el-table-column label="优先级" align="center" prop="priorityLevel" width="100" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="getPriorityType(scope.row.priorityLevel)" size="mini">
            {{ scope.row.priorityLevel }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="项目里程碑" align="center" prop="milestoneDisplay" min-width="120">
        <template slot-scope="scope">
          <div class="milestone-content" v-html="scope.row.milestoneDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="任务说明/进度" align="center" prop="progressDisplay" min-width="150">
        <template slot-scope="scope">
          <div class="progress-content" v-html="scope.row.progressDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="干系人" align="center" prop="stakeholderDisplay" min-width="120">
        <template slot-scope="scope">
          <div class="stakeholder-content" v-html="scope.row.stakeholderDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="投入人力" align="center" prop="manpowerDisplay" width="100">
        <template slot-scope="scope">
          <div class="manpower-content" v-html="scope.row.manpowerDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="工作量(人日)" align="center" prop="workloadDisplay" width="120">
        <template slot-scope="scope">
          <div class="workload-content" v-html="scope.row.workloadDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="需求背景" align="center" prop="requirementBackground" min-width="120" show-overflow-tooltip />
      
      <el-table-column label="负责项目经理" align="center" prop="projectManagers" min-width="100" show-overflow-tooltip />
      
      <el-table-column label="更新时间" align="center" prop="updatedTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.updatedTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="完成时间" align="center" prop="completionTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.completionTime) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMajor, dict.type.project_outcome_business_category_major) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMinor, dict.type.project_outcome_business_category_minor) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建人" align="center" prop="createdBy" min-width="100" />
      
      <el-table-column label="创建时间" align="center" prop="createdTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="180" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleSync(scope.row)">同步</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">查看详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增编辑弹窗 -->
    <addEditDialog
      :dialogVisible.sync="addEditDialog.show"
      :dialogTitle="addEditDialog.title"
      :dialogData="addEditDialog.data"
      @callback="handleQuery">
    </addEditDialog>

    <!-- 详情弹窗 -->
    <detailDialog
      :dialogVisible.sync="detailDialog.show"
      :dialogTitle="detailDialog.title"
      :recordId="detailDialog.recordId">
    </detailDialog>
  </div>
</template>

<script>
// 导入API接口
import {
  projectResultList,
  projectResultDelete,
  projectResultSync,
  getBusinessTypeOptions
} from "@/api/project/projectResult"
import addEditDialog from "./components/addEditDialog.vue"
import detailDialog from "./components/detailDialog.vue"
import { parseTime } from "@/utils/ruoyi"

export default {
  name: "ProjectResult",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  components: {
    addEditDialog,
    detailDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessTypeId: "",
        status: "",
        projectManagers: null,
        projectTaskName: "",
        resultType: null,
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        priorityLevel: "",
        archiveFlag: null,
        createTimeRange: []
      },
      // 弹窗配置
      addEditDialog: {
        show: false,
        title: '新增',
        data: {}
      },
      // 详情弹窗配置
      detailDialog: {
        show: false,
        title: '项目成果详情',
        recordId: null
      },
      // 业务类型选项
      businessTypeOptions: [],

    }
  },
  created() {
    this.getList()
    this.loadOptions()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      
      // 处理时间范围参数和分页参数
      let params = { ...this.queryParams }
      if (this.queryParams.createTimeRange && this.queryParams.createTimeRange.length !== 0) {
        params.createTimeStart = this.queryParams.createTimeRange[0]
        params.createTimeEnd = this.queryParams.createTimeRange[1]
      }
      delete params.createTimeRange
      
      // 清理空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      // API调用
      projectResultList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          // 处理数据，添加拼接字段
          this.tableData = (res.rows || []).map(item => {
            return {
              ...item,
              milestoneDisplay: this.formatMilestone(item),
              progressDisplay: this.formatProgress(item),
              stakeholderDisplay: this.formatStakeholders(item),
              manpowerDisplay: this.formatManpower(item),
              workloadDisplay: this.formatWorkload(item)
            }
          })
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('接口调用失败')
      })
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    
    /** 业务类型管理 */
    handleBusinessTypeManage() {
      this.$message.info('业务类型管理功能待开发')
      // TODO: 跳转到业务类型管理页面
    },
    
    /** 新增 */
    handleAdd() {
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增项目成果'
      this.addEditDialog.data = {}
    },
    
    /** 编辑 */
    handleEdit(row) {
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑项目成果'
      this.addEditDialog.data = { ...row }
    },
    
    /** 同步 */
    handleSync(row) {
      this.$confirm(`确认同步项目"${row.projectTaskName}"的数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultSync(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('同步成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '同步失败')
          }
        }).catch(() => {
          this.$message.error('同步失败')
        })
      }).catch(() => {
        this.$message.info('已取消同步')
      })
    },
    
    /** 查看详情 */
    handleViewDetail(row) {
      this.detailDialog.show = true
      this.detailDialog.title = `项目成果详情：${row.projectTaskName}`
      this.detailDialog.recordId = row.id
    },
    
    /** 删除 */
    handleDelete(row) {
      this.$confirm(`确认删除项目"${row.projectTaskName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultDelete(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    /** 排序 */
    handleSortChange({ column, prop, order }) {
      this.queryParams.orderByColumn = prop
      this.queryParams.isAsc = order
      this.handleQuery()
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning', 
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },
    
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },



    /** 加载下拉选项数据 */
    loadOptions() {
      // 加载业务类型选项
      getBusinessTypeOptions().then(res => {
        if (res.code === 200 && res.rows) {
          // 将分页数据转换为下拉选项格式
          this.businessTypeOptions = res.rows.map(item => ({
            value: item.id,
            label: item.businessTypeName
          }))
        }
      }).catch(() => {
        console.warn('获取业务类型选项失败')
      })
    },

    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      return parseTime(date, '{y}-{m}-{d}')
    },

    /** 格式化项目里程碑 */
    formatMilestone(row) {
      const milestones = []
      if (row.milestoneRequirements) {
        milestones.push(`完成评审：${this.formatDate(row.milestoneRequirements)}`)
      }
      if (row.milestoneDevelopment) {
        milestones.push(`完成开发：${this.formatDate(row.milestoneDevelopment)}`)
      }
      if (row.milestoneTest) {
        milestones.push(`完成测试验收：${this.formatDate(row.milestoneTest)}`)
      }
      if (row.milestoneOnline) {
        milestones.push(`完成上线：${this.formatDate(row.milestoneOnline)}`)
      }
      return milestones.join('<br/>')
    },

    /** 格式化任务说明/进度 */
    formatProgress(row) {
      const progress = []
      if (row.requirementsProgress !== null && row.requirementsProgress !== undefined) {
        progress.push(`需求评审：${row.requirementsProgress}%`)
      }
      if (row.developmentProgress !== null && row.developmentProgress !== undefined) {
        progress.push(`开发进度：${row.developmentProgress}%`)
      }
      if (row.testProgress !== null && row.testProgress !== undefined) {
        progress.push(`测试验收进度：${row.testProgress}%`)
      }
      return progress.join('<br/>')
    },

    /** 格式化干系人 */
    formatStakeholders(row) {
      const stakeholders = []
      if (row.productManagers) {
        stakeholders.push(`产品：${row.productManagers}`)
      }
      if (row.devTeams) {
        const devTeamNames = this.convertDeptIdsToNames(row.devTeams, 'dev')
        stakeholders.push(`开发：${devTeamNames}`)
      }
      if (row.testTeams) {
        const testTeamNames = this.convertDeptIdsToNames(row.testTeams, 'test')
        stakeholders.push(`测试：${testTeamNames}`)
      }
      return stakeholders.join('<br/>')
    },

    /** 格式化投入人力 */
    formatManpower(row) {
      const manpower = []
      if (row.devManpower) {
        manpower.push(`开发：${row.devManpower}人`)
      }
      if (row.testManpower) {
        manpower.push(`测试：${row.testManpower}人`)
      }
      return manpower.join('<br/>')
    },

    /** 格式化工作量（人日） */
    formatWorkload(row) {
      const workload = []
      if (row.devWorkload) {
        workload.push(`开发：${row.devWorkload}人日`)
      }
      if (row.testWorkload) {
        workload.push(`测试：${row.testWorkload}人日`)
      }
      return workload.join('<br/>')
    },

    /** 将部门ID转换为部门名称 */
    convertDeptIdsToNames(deptIds, type) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const deptDict = type === 'dev' ? this.dict.type.project_outcome_dev_dept : this.dict.type.project_outcome_test_dept
      if (!deptDict) return deptIds

      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    }

  }
}
</script>

<style scoped>
.query-form {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.milestone-content {
  line-height: 1.5;
}

.progress-content {
  text-align: left;
}

/* 表格单元格内容样式 */
.el-table .cell {
  line-height: 1.4;
}

/* 操作按钮样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}
</style>
